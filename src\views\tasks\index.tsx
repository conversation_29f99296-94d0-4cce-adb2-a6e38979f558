import React, { useState, useEffect } from 'react';
import { Table, Button, Input, Select, Space, Tag } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import CreateTaskModal from './components/create-task-modal';
import TaskDetailModal from './components/task-detail-modal';
import styles from './index.module.css';

interface TaskData {
  id: string;
  taskName: string;
  taskType: string;
  participants: string;
  status: 'running' | 'completed' | 'failed' | 'pending';
  createTime: string;
}

const mockData: TaskData[] = [
  {
    id: '1',
    taskName: '任务1',
    taskType: 'bob',
    participants: '实功',
    status: 'running',
    createTime: '2025-07-01 14:00:00',
  },
];

const TaskCenter: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [createTaskVisible, setCreateTaskVisible] = useState(false);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskData | null>(null);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TaskData[]>([]);

  useEffect(() => {
    // 模拟加载数据
    setLoading(true);
    setTimeout(() => {
      setData(mockData);
      setLoading(false);
    }, 300);
  }, []);

  // 筛选选项
  const statusOptions = [
    { value: 'all', label: '全部状态' },
    { value: 'running', label: '运行中' },
    { value: 'completed', label: '已完成' },
    { value: 'failed', label: '失败' },
    { value: 'pending', label: '等待中' },
  ];

  const typeOptions = [
    { value: 'all', label: '全部类型' },
    { value: 'training', label: '训练任务' },
    { value: 'inference', label: '推理任务' },
    { value: 'evaluation', label: '评估任务' },
  ];

  // 过滤数据
  const filteredData = data.filter((item) => {
    const nameMatch = item.taskName.toLowerCase().includes(searchText.toLowerCase());
    const statusMatch = statusFilter === 'all' || item.status === statusFilter;
    const typeMatch = typeFilter === 'all' || item.taskType === typeFilter;
    return nameMatch && statusMatch && typeMatch;
  });

  // 状态渲染
  const renderStatus = (status: string) => {
    const statusConfig = {
      running: { color: 'processing', text: '实功' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      pending: { color: 'default', text: '等待中' },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列配置
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '任务名',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
    },
    {
      title: '参与方',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatus,
    },
    {
      title: '修改时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: TaskData) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small"
            onClick={() => handleDownload(record)}
          >
            下载结果
          </Button>
        </Space>
      ),
    },
  ];

  // 处理函数
  const handleCreateTask = () => {
    setCreateTaskVisible(true);
  };

  const handleCreateTaskClose = () => {
    setCreateTaskVisible(false);
  };

  const handleViewDetail = (task: TaskData) => {
    setSelectedTask(task);
    setTaskDetailVisible(true);
  };

  const handleTaskDetailClose = () => {
    setTaskDetailVisible(false);
    setSelectedTask(null);
  };

  const handleDownload = (task: TaskData) => {
    console.log('下载任务结果:', task);
    // 这里实现下载逻辑
  };

  return (
    <div className={styles.taskContainer}>
      {/* 搜索和筛选栏 */}
      <div className={styles.searchBar}>
        <Input
          placeholder="输入关键词搜索"
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
          allowClear
          prefix={<SearchOutlined />}
        />
        <Select
          defaultValue="all"
          className={styles.filterSelect}
          options={statusOptions}
          placeholder="状态筛选"
          onChange={(value) => setStatusFilter(value)}
        />
        <Select
          defaultValue="all"
          className={styles.filterSelect}
          options={typeOptions}
          placeholder="任务类型"
          onChange={(value) => setTypeFilter(value)}
        />
        <Button
          type="primary"
          className={styles.createButton}
          icon={<PlusOutlined />}
          onClick={handleCreateTask}
        >
          新建任务
        </Button>
      </div>

      {/* 任务表格 */}
      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        className={styles.taskTable}
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `共 ${total} 条记录`,
        }}
      />

      {/* 创建任务弹窗 */}
      <CreateTaskModal
        open={createTaskVisible}
        onClose={handleCreateTaskClose}
      />

      {/* 任务详情弹窗 */}
      <TaskDetailModal
        open={taskDetailVisible}
        onClose={handleTaskDetailClose}
        task={selectedTask}
      />
    </div>
  );
};

export default TaskCenter;
