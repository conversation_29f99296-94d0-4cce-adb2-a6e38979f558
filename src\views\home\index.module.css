.homeContainer {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.statsCard {
  transition: all 0.3s ease;
}

.statsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statsTitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.statsValue {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.statsTrend {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.chartCard {
  transition: all 0.3s ease;
}

.chartCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tableCard {
  margin-top: 24px;
}

.chartPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  color: #999;
}

.chartPlaceholderContent {
  text-align: center;
}

.chartPlaceholderTitle {
  font-size: 16px;
  margin-bottom: 8px;
}

.chartPlaceholderSubtitle {
  font-size: 12px;
}
