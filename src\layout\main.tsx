import { Layout, Menu, MenuProps } from 'antd';
import {
  DatabaseOutlined,
  FileTextOutlined,
  KeyOutlined,
  MessageOutlined,
  HomeOutlined,
  PlaySquareOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';

import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import PageHeader from './header';
import useUserStore from '@/store/user';
const { Sider, Content, Header } = Layout;

type MenuItem = Required<MenuProps>['items'][number];
const navItems: MenuItem[] = [
  {
    key: 'home',
    label: '首页',
    icon: <HomeOutlined />,
  },
  {
    key: 'datasets',
    label: '数据管理',
    icon: <DatabaseOutlined />,
  },
  {
    key: 'tasks',
    label: '任务中心',
    icon: <DatabaseOutlined />,
  },
  {
    key: 'keys',
    label: '密钥管理',
    icon: <KeyOutlined />,
  },
  {
    key: 'messages',
    label: '消息管理',
    icon: <MessageOutlined />,
  },
  {
    key: 'logs',
    label: '日志管理',
    icon: <FileTextOutlined />,
  },
];

const LayoutMain = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const user = useUserStore((state) => state.user);

  const goto = (key: string) => {
    navigate(key);
  };

  useEffect(() => {
    const currMenu = location.pathname.split('/')[1];
    if (!currMenu) {
      navigate('/', { replace: true });
    } else setSelectedKeys([currMenu]);
  }, [location.pathname, navigate]);

  return (
    <Layout className="h-screen overflow-hidden">
      <Header className="bg-white border-b">
        <PageHeader />
      </Header>
      <Layout>
        {user?.role === 1 && (
          <Sider theme="light" width={260}>
            <Menu
              mode="inline"
              items={navItems}
              selectedKeys={selectedKeys}
              onSelect={(e) => goto(e.key)}
            ></Menu>
          </Sider>
        )}
        <Content className="overflow-y-auto">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutMain;
