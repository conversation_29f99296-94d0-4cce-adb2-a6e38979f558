import { Navigate } from 'react-router-dom';
import useUserStore from '@/store/user';

// Mock function to get user role, replace with actual implementation
const getUserRole = () => {
  // This should return 'admin' or 'user' based on the logged-in user's role
  return 'admin'; // Example: return 'admin';
};

// Role-based route component
interface RoleBasedRouteProps {
  element: JSX.Element;
  allowedRoles: number[];
}

export const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  element,
  allowedRoles,
}) => {
  const user = useUserStore((state) => state.user);
  // const user = {
  //   role: 1,
  // };
  console.log(user, 'user');
  return allowedRoles.includes(user?.role ?? 1) ? element : <Navigate to="/no-auth" />;
};
