import React, { useState, useEffect } from 'react';
import { Table, Button, Input, Select, Tag, Tabs, Radio } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import styles from './index.module.css';

interface MessageData {
  id: string;
  initiator: string;
  taskName: string;
  processStatus: string;
  submitTime: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
}

const mockInitiatedData: MessageData[] = [
  {
    id: 'xxxxx',
    initiator: 'Alice',
    taskName: '任务1',
    processStatus: '审核中',
    submitTime: '2025-07-01 14:00:00',
    status: 'processing',
  },
];

const mockProcessedData: MessageData[] = [
  {
    id: 'yyyyy',
    initiator: 'Bob',
    taskName: '任务2',
    processStatus: '已完成',
    submitTime: '2025-07-01 13:00:00',
    status: 'completed',
  },
];

const MessageCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('initiated');
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [processFilter, setProcessFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [initiatedData, setInitiatedData] = useState<MessageData[]>([]);
  const [processedData, setProcessedData] = useState<MessageData[]>([]);

  useEffect(() => {
    // 模拟加载数据
    setLoading(true);
    setTimeout(() => {
      setInitiatedData(mockInitiatedData);
      setProcessedData(mockProcessedData);
      setLoading(false);
    }, 300);
  }, []);

  // 获取当前标签页的数据
  const getCurrentData = () => {
    return activeTab === 'initiated' ? initiatedData : processedData;
  };

  // 筛选选项
  const statusOptions = [
    { value: 'all', label: '全部' },
    { value: 'pending', label: '待处理' },
    // { value: 'processing', label: '处理中' },
    { value: 'completed', label: '已处理' },
    // { value: 'rejected', label: '已拒绝' },
  ];

  const processOptions = [
    { value: 'all', label: '全部类型' },
    { value: 'approval', label: '审批' },
    { value: 'review', label: '审核' },
    { value: 'notification', label: '通知' },
  ];

  // 过滤数据
  const filteredData = getCurrentData().filter((item) => {
    const nameMatch =
      item.taskName.toLowerCase().includes(searchText.toLowerCase()) ||
      item.initiator.toLowerCase().includes(searchText.toLowerCase());
    const statusMatch = statusFilter === 'all' || item.status === statusFilter;
    return nameMatch && statusMatch;
  });

  // 状态渲染
  const renderStatus = (status: string, processStatus: string) => {
    const statusConfig = {
      pending: { color: 'default', text: '待处理' },
      processing: { color: 'processing', text: '处理中' },
      completed: { color: 'success', text: '已完成' },
      rejected: { color: 'error', text: '已拒绝' },
    };

    // 如果有具体的处理状态，优先显示处理状态
    if (processStatus) {
      return <span>{processStatus}</span>;
    }

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列配置
  const columns = [
    {
      title: '消息ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '发起机构',
      dataIndex: 'initiator',
      key: 'initiator',
      width: 120,
    },
    {
      title: '任务名',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
    },
    {
      title: '流程状态',
      dataIndex: 'processStatus',
      key: 'processStatus',
      width: 120,
      render: (processStatus: string, record: MessageData) =>
        renderStatus(record.status, processStatus),
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      width: 180,
    },
    {
      title: '类型',
      key: 'type',
      width: 120,
      render: () => <span>审(已完成)&(待完成)</span>,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: unknown, record: MessageData) => (
        <Button type="link" size="small" onClick={() => handleViewDetail(record)}>
          详情
        </Button>
      ),
    },
  ];

  // 处理函数
  const handleViewDetail = (message: MessageData) => {
    console.log('查看详情:', message);
    // 这里实现查看详情逻辑
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换标签页时重置筛选条件
    setSearchText('');
    setStatusFilter('all');
    setProcessFilter('all');
  };

  const tabItems = [
    {
      key: 'initiated',
      label: '我发起的',
    },
    {
      key: 'processed',
      label: '我处理的',
    },
  ];

  return (
    <div className={styles.messageContainer}>
      {/* 标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        className={styles.messageTabs}
      />

      {/* 搜索和筛选栏 */}
      <div className={styles.searchBar}>
        <Radio.Group
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          {statusOptions.map((option) => (
            <Radio.Button key={option.value} value={option.value}>
              {option.label}
            </Radio.Button>
          ))}
        </Radio.Group>
        <Select
          defaultValue="all"
          className={styles.filterSelect}
          options={processOptions}
          placeholder="全部类型"
          onChange={(value) => setProcessFilter(value)}
        />
        <Input
          placeholder="输入搜索关键词"
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
          allowClear
          suffix={<SearchOutlined />}
        />
      </div>

      {/* 消息表格 */}
      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        className={styles.messageTable}
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
    </div>
  );
};

export default MessageCenter;
