import React, { useState } from 'react';
import { Modal, Form, Input, Select, Button, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface DataProvider {
  organization: string;
  data: string;
}

interface CreateTaskModalProps {
  open: boolean;
  onClose: () => void;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();
  const [dataProviders, setDataProviders] = useState<DataProvider[]>([
    { organization: 'bob', data: 'bob_data' },
  ]);
  const [functionProviders, setFunctionProviders] = useState<DataProvider[]>([
    { organization: 'alice', data: 'alice_function' },
  ]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建任务:', values);

      // 这里处理任务创建逻辑
      message.success('任务创建成功');
      form.resetFields();
      onClose();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  const addDataProvider = () => {
    setDataProviders([...dataProviders, { organization: '', data: '' }]);
  };

  const addFunctionProvider = () => {
    setFunctionProviders([...functionProviders, { organization: '', data: '' }]);
  };

  const updateDataProvider = (
    index: number,
    field: keyof DataProvider,
    value: string,
  ) => {
    const newProviders = [...dataProviders];
    newProviders[index][field] = value;
    setDataProviders(newProviders);
  };

  const updateFunctionProvider = (
    index: number,
    field: keyof DataProvider,
    value: string,
  ) => {
    const newProviders = [...functionProviders];
    newProviders[index][field] = value;
    setFunctionProviders(newProviders);
  };

  return (
    <Modal
      title="新建任务"
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          resultReceiver: 'alice',
          resultFileName: 'a',
        }}
      >
        {/* 任务名称 */}
        <Form.Item
          name="taskName"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入中文、大小写字母、数字、下划线、中横线，32个字符以内" />
        </Form.Item>

        {/* 数据提供方 */}
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 16, fontWeight: 500 }}>数据提供方</div>
          {dataProviders.map((provider, index) => (
            <div key={index} style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <div style={{ marginBottom: 8, fontSize: 14, color: '#666' }}>机构</div>
                <Select
                  value={provider.organization}
                  onChange={(value) => updateDataProvider(index, 'organization', value)}
                  style={{ width: '100%' }}
                  options={[
                    { value: 'bob', label: 'bob' },
                    { value: 'alice', label: 'alice' },
                    { value: 'charlie', label: 'charlie' },
                  ]}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8, fontSize: 14, color: '#666' }}>数据</div>
                <Select
                  value={provider.data}
                  onChange={(value) => updateDataProvider(index, 'data', value)}
                  style={{ width: '100%' }}
                  options={[
                    { value: 'bob_data', label: 'bob_data' },
                    { value: 'alice_data', label: 'alice_data' },
                    { value: 'charlie_data', label: 'charlie_data' },
                  ]}
                />
              </div>
            </div>
          ))}
          <Button
            type="dashed"
            onClick={addDataProvider}
            icon={<PlusOutlined />}
            style={{ width: '100%', marginTop: 8 }}
          >
            新增一组数据
          </Button>
        </div>

        {/* 函数提供方 */}
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 16, fontWeight: 500 }}>函数提供方</div>
          {functionProviders.map((provider, index) => (
            <div key={index} style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <div style={{ marginBottom: 8, fontSize: 14, color: '#666' }}>机构</div>
                <Select
                  value={provider.organization}
                  onChange={(value) =>
                    updateFunctionProvider(index, 'organization', value)
                  }
                  style={{ width: '100%' }}
                  options={[
                    { value: 'alice', label: 'alice' },
                    { value: 'bob', label: 'bob' },
                    { value: 'charlie', label: 'charlie' },
                  ]}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8, fontSize: 14, color: '#666' }}>数据</div>
                <Select
                  value={provider.data}
                  onChange={(value) => updateFunctionProvider(index, 'data', value)}
                  style={{ width: '100%' }}
                  options={[
                    { value: 'alice_function', label: 'alice_function' },
                    { value: 'bob_function', label: 'bob_function' },
                    { value: 'charlie_function', label: 'charlie_function' },
                  ]}
                />
              </div>
            </div>
          ))}
          <Button
            type="dashed"
            onClick={addFunctionProvider}
            icon={<PlusOutlined />}
            style={{ width: '100%', marginTop: 8 }}
          >
            新增一组数据
          </Button>
        </div>

        {/* 结果接收方 */}
        <Form.Item
          name="resultReceiver"
          label="结果接收方"
          rules={[{ required: true, message: '请选择结果接收方' }]}
        >
          <Select
            options={[
              { value: 'alice', label: 'alice' },
              { value: 'bob', label: 'bob' },
              { value: 'charlie', label: 'charlie' },
            ]}
          />
        </Form.Item>

        {/* 结果文件名称 */}
        <Form.Item
          name="resultFileName"
          label="结果文件名称"
          rules={[{ required: true, message: '请输入结果文件名称' }]}
        >
          <Select
            options={[
              { value: 'a', label: 'a' },
              { value: 'result', label: 'result' },
              { value: 'output', label: 'output' },
            ]}
          />
        </Form.Item>

        {/* 操作按钮 */}
        <Form.Item style={{ marginTop: 32, marginBottom: 0 }}>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12 }}>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={handleSubmit}>
              完成
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateTaskModal;
