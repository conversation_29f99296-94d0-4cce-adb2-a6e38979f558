import request from '@/request';

export const addUser = (data: any) => {
  return request.post('/system/user', data);
};

export interface ParamsGetUserList {
  pageNum: number;
  pageSize: number;
  username?: string;
  role?: number;
}
export const getUserList = (data: ParamsGetUserList) => {
  return request.get('/system/user/page', { params: data });
};

export const updateUser = (data: any) => {
  return request.put(`/system/user`, data);
};

export const deleteUser = (id: string) => {
  return request.delete(`/system/user/${id}`);
};
