import { Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

const Page404 = () => {
  const navigate = useNavigate();
  return (
    <div className="w-screen h-screen flex flex-col items-center pt-40">
      <h1 className="text-4xl font-bold mb-4">此页面不存在...</h1>
      <Typography.Link onClick={() => navigate(-1)}>返回上一页</Typography.Link>
    </div>
  );
};

export default Page404;
