import React, { useState } from 'react';
import { Modal, Table, Button, Space, Input, Select, Form, message } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

interface AuthRecord {
  id: string;
  username: string;
  role: string;
  permissions: string[];
  createTime: string;
}

interface AuthManageModalProps {
  open: boolean;
  onClose: () => void;
}

const AuthManageModal: React.FC<AuthManageModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();
  const [authData, setAuthData] = useState<AuthRecord[]>([
    {
      id: '1',
      username: 'alice',
      role: '数据提供方',
      permissions: ['读取', '查询'],
      createTime: '2023-12-15 10:30:00',
    },
    {
      id: '2',
      username: 'bob',
      role: '数据使用方',
      permissions: ['查询'],
      createTime: '2023-12-15 11:00:00',
    },
  ]);
  const [showAddForm, setShowAddForm] = useState(false);

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 150,
      render: (permissions: string[]) => permissions.join(', '),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: AuthRecord) => (
        <Button
          type="link"
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteAuth(record.id)}
        >
          删除
        </Button>
      ),
    },
  ];

  const handleAddAuth = async () => {
    try {
      const values = await form.validateFields();
      const newAuth: AuthRecord = {
        id: Date.now().toString(),
        username: values.username,
        role: values.role,
        permissions: values.permissions,
        createTime: new Date().toLocaleString('zh-CN'),
      };
      setAuthData([...authData, newAuth]);
      form.resetFields();
      setShowAddForm(false);
      message.success('授权添加成功');
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleDeleteAuth = (id: string) => {
    setAuthData(authData.filter(item => item.id !== id));
    message.success('授权删除成功');
  };

  const handleCancel = () => {
    form.resetFields();
    setShowAddForm(false);
    onClose();
  };

  return (
    <Modal
      title="授权管理"
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          关闭
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <div className="mb-4">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setShowAddForm(true)}
        >
          添加授权
        </Button>
      </div>

      {showAddForm && (
        <div className="mb-4 p-4 border border-gray-200 rounded">
          <Form form={form} layout="inline">
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input placeholder="用户名" style={{ width: 120 }} />
            </Form.Item>
            <Form.Item
              name="role"
              rules={[{ required: true, message: '请选择角色' }]}
            >
              <Select placeholder="角色" style={{ width: 120 }}>
                <Select.Option value="数据提供方">数据提供方</Select.Option>
                <Select.Option value="数据使用方">数据使用方</Select.Option>
                <Select.Option value="管理员">管理员</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="permissions"
              rules={[{ required: true, message: '请选择权限' }]}
            >
              <Select
                mode="multiple"
                placeholder="权限"
                style={{ width: 150 }}
              >
                <Select.Option value="读取">读取</Select.Option>
                <Select.Option value="写入">写入</Select.Option>
                <Select.Option value="查询">查询</Select.Option>
                <Select.Option value="删除">删除</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={handleAddAuth}>
                  确定
                </Button>
                <Button onClick={() => setShowAddForm(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={authData}
        rowKey="id"
        pagination={false}
        size="small"
      />
    </Modal>
  );
};

export default AuthManageModal;
