import { message } from 'antd';
import axios from 'axios';

const request = axios.create({
  baseURL: '/api',
});

request.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = token;
  }
  return config;
});

request.interceptors.response.use(
  (response) => {
    const data = response.data;
    console.log(data);
    if (data.code !== 200) {
      message.error(data.message);
      return Promise.reject(data);
    }
    return response.data;
  },
  (error) => {
    console.log(error, 'error');
    message.error(error.message || '未知错误');
  },
);

export default request;
