import request from '@/request';
interface LoginData {
  username: string;
  password: string;
}

export const login = async (data: LoginData) => {
  const response = await request.post('/auth/login', data);
  console.log(response);
  const { token } = response.data;
  if (token) {
    localStorage.setItem('token', token);
  }
  return response;
};

export const logout = async () => {
  const response = await request.post('/auth/logout');
  localStorage.removeItem('token');
  return response;
};

export const getUserInfo = async () => {
  const response = await request.post('/auth/info');
  return response.data;
};
