.taskContainer {
  padding: 24px;
  background-color: #fff;
  min-height: 100vh;
}

.searchBar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.searchInput {
  width: 240px;
}

.filterSelect {
  width: 120px;
}

.createButton {
  margin-left: auto;
  background-color: #1890ff;
  border-color: #1890ff;
}

.createButton:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.taskTable {
  background: #fff;
  border-radius: 6px;
}

.taskTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.taskTable .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.statusTag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.actionButtons .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
}

.actionButtons .ant-btn-link:hover {
  color: #40a9ff;
}

/* 弹窗样式 */
.modalForm {
  margin-top: 16px;
}

.modalForm .ant-form-item {
  margin-bottom: 16px;
}

.modalFormActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.detailModal {
  max-width: 800px;
}

.detailSection {
  margin-bottom: 24px;
}

.detailSection h4 {
  margin-bottom: 12px;
  color: #262626;
  font-weight: 600;
}

.detailItem {
  display: flex;
  margin-bottom: 8px;
}

.detailLabel {
  width: 120px;
  color: #8c8c8c;
  flex-shrink: 0;
}

.detailValue {
  color: #262626;
  flex: 1;
}

.progressSection {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.logSection {
  background-color: #f6f6f6;
  padding: 16px;
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.logContent {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #262626;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .searchBar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .searchInput,
  .filterSelect {
    width: 100%;
  }
  
  .createButton {
    margin-left: 0;
    width: 100%;
  }
  
  .taskTable {
    overflow-x: auto;
  }
}
