import React from 'react';
import { Modal, Form, Input, Select } from 'antd';

const { Option } = Select;

export interface AddUserModalProps {
  open: boolean;
  onOk: (values: any) => void;
  onCancel: () => void;
  confirmLoading?: boolean;
}

const AddUserModal: React.FC<AddUserModalProps> = ({
  open,
  onOk,
  onCancel,
  confirmLoading,
}) => {
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
      form.resetFields();
    } catch {
      /* ignore validation error */
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加用户"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label="账号"
          name="username"
          rules={[{ required: true, message: '请输入账号' }]}
        >
          <Input placeholder="请输入内容" />
        </Form.Item>
        <Form.Item
          label="用户名"
          name="name"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="初始密码"
          name="password"
          rules={[{ required: true, message: '请输入初始密码' }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="角色"
          name="role"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Select placeholder="请选择角色">
            <Option value="管理员">管理员</Option>
            <Option value="普通用户">普通用户</Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddUserModal;
