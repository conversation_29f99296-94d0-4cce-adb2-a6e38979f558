import React, { useState, useEffect } from 'react';
import { Table, Button, Input, Select } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import AddDataSourceModal from './components/add-datasource-modal.tsx';
import AuthManageModal from './components/auth-manage-modal.tsx';
import styles from './index.module.css';

interface DatasetData {
  id: string;
  dataSourceName: string;
  dataConnectionType: string;
  connectedTables: string;
  rootPassword: string;
  datasetPassword: string;
  authorizedUser: string;
  status: 'running' | 'stopped';
}

const mockData: DatasetData[] = [
  {
    id: '1',
    dataSourceName: 'LOCAL',
    dataConnectionType: '总客户，共读取一个 sym1',
    connectedTables: 'dk1',
    rootPassword: 'alice',
    datasetPassword: '',
    authorizedUser: '',
    status: 'running',
  },
];

const DatasetList: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedSource, setSelectedSource] = useState('all');
  const [addDataSourceVisible, setAddDataSourceVisible] = useState(false);
  const [authManageVisible, setAuthManageVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DatasetData[]>([]);

  useEffect(() => {
    // 模拟加载数据
    setLoading(true);
    setTimeout(() => {
      setData(mockData);
      setLoading(false);
    }, 300);
  }, []);

  // Filter options
  const sourceOptions = [
    { value: 'all', label: '全部' },
    { value: 'LOCAL', label: 'LOCAL' },
    { value: 'REMOTE', label: 'REMOTE' },
  ];

  const filteredData = data.filter((item) => {
    const nameMatch = item.dataSourceName
      .toLowerCase()
      .includes(searchText.toLowerCase());
    const sourceMatch =
      selectedSource === 'all' || item.dataSourceName === selectedSource;

    return nameMatch && sourceMatch;
  });

  const columns = [
    {
      title: '数据源名称',
      dataIndex: 'dataSourceName',
      key: 'dataSourceName',
      width: 120,
    },
    {
      title: '数据连接类型',
      dataIndex: 'dataConnectionType',
      key: 'dataConnectionType',
      width: 200,
    },
    {
      title: '已连接表名',
      dataIndex: 'connectedTables',
      key: 'connectedTables',
      width: 120,
    },
    {
      title: '根密码名称',
      dataIndex: 'rootPassword',
      key: 'rootPassword',
      width: 120,
    },
    {
      title: '数据密码名称',
      dataIndex: 'datasetPassword',
      key: 'datasetPassword',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '所属用户',
      dataIndex: 'authorizedUser',
      key: 'authorizedUser',
      width: 100,
      render: (text: string) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: 'running' | 'stopped') => (
        <div className={styles.statusIndicator}>
          <div
            className={`${styles.statusDot} ${
              status === 'running' ? styles.statusDotRunning : styles.statusDotStopped
            }`}
          />
          <span>{status === 'running' ? '未引用' : '已停止'}</span>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: 200,
      render: () => (
        <div className={styles.operationButtons}>
          <Button type="link" size="small">
            刷新
          </Button>
          <Button type="link" size="small" onClick={() => setAuthManageVisible(true)}>
            授权管理
          </Button>
          <Button type="link" size="small" danger>
            删除
          </Button>
        </div>
      ),
    },
  ];

  const handleAddDataSource = () => {
    setAddDataSourceVisible(true);
  };

  const handleAddDataSourceClose = () => {
    setAddDataSourceVisible(false);
  };

  const handleAuthManageClose = () => {
    setAuthManageVisible(false);
  };

  return (
    <div className={styles.datasetContainer}>
      <div className={styles.searchBar}>
        <Input
          placeholder="搜索名称"
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
          allowClear
          prefix={<SearchOutlined />}
        />
        <Select
          defaultValue="all"
          className={styles.filterSelect}
          options={sourceOptions}
          placeholder="数据源类型"
          onChange={(value) => setSelectedSource(value)}
        />
        <Button
          type="primary"
          className={styles.addButton}
          icon={<PlusOutlined />}
          onClick={handleAddDataSource}
        >
          添加数据源
        </Button>
      </div>
      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
      <AddDataSourceModal
        open={addDataSourceVisible}
        onClose={handleAddDataSourceClose}
      />
      <AuthManageModal open={authManageVisible} onClose={handleAuthManageClose} />
    </div>
  );
};

export default DatasetList;
