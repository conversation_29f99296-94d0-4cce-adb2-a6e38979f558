import React, { useEffect, useState, useCallback } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Tag,
  Popconfirm,
  message,
  Space,
  Typography,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import AddUserModal from './components/add-user';
import { getUserList, ParamsGetUserList } from './user.service';

const { Option } = Select;

interface User {
  userId: string;
  username: string;
  name: string;
  role: string;
  status: string;
}

const initialUsers: User[] = [
  {
    userId: '1',
    username: 'wangsf',
    name: '王三丰',
    role: '管理员',
    status: '可用',
  },
  {
    userId: '2',
    username: 'z<PERSON><PERSON>',
    name: '张三',
    role: '普通用户',
    status: '禁用',
  },
];

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 5, total: 0 });
  const [addUserOpen, setAddUserOpen] = useState(false);
  const [addUserLoading, setAddUserLoading] = useState(false);

  const handleStatusChange = (key: string, status: string) => {
    setUsers(users.map((user) => (user.userId === key ? { ...user, status } : user)));
    message.success(`用户已${status === '可用' ? '启用' : '禁用'}`);
  };

  const handleDelete = (key: string) => {
    setUsers(users.filter((user) => user.userId !== key));
    message.success('用户已删除');
  };

  const handleAddUser = async (values: any) => {
    setAddUserLoading(true);
    setTimeout(() => {
      setUsers([
        ...users,
        {
          key: String(Date.now()),
          ...values,
          status: '可用',
        },
      ]);
      setAddUserLoading(false);
      setAddUserOpen(false);
      message.success('用户添加成功');
    }, 500);
  };

  const filteredUsers = users.filter((user) => {
    return (
      (!search || user.username.includes(search) || user.name.includes(search)) &&
      (!roleFilter || user.role === roleFilter)
    );
  });

  const columns = [
    {
      title: '账号',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === '可用' ? 'green' : 'red'}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          {record.status === '可用' ? (
            <Popconfirm
              title="确定要禁用该用户吗？"
              onConfirm={() => handleStatusChange(record.userId, '禁用')}
            >
              <Typography.Link>禁用</Typography.Link>
            </Popconfirm>
          ) : (
            <Popconfirm
              title="确定要启用该用户吗？"
              onConfirm={() => handleStatusChange(record.userId, '可用')}
            >
              <Typography.Link>启用</Typography.Link>
            </Popconfirm>
          )}
          <Popconfirm
            title="确定要删除该用户吗？"
            onConfirm={() => handleDelete(record.userId)}
          >
            <Typography.Link type="danger">删除</Typography.Link>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const getUsers = useCallback(async () => {
    console.log(pagination, search, roleFilter);
    const params: ParamsGetUserList = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      username: search || undefined,
      // role: Number(roleFilter),
    };
    // const res = await getUserList(params);
    // console.log(res, 'res');
    // setUsers(res.data);
    // setPagination({
    //   ...pagination,
    //   total: res.total,
    // })
  }, [pagination, search, roleFilter]);

  useEffect(() => {
    getUsers();
  }, [pagination, search, roleFilter, getUsers]);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setUsers(initialUsers);
      setLoading(false);
    }, 500);
  }, []);

  return (
    <div className="p-6 bg-white min-h-[360px]">
      <div className="mb-4 flex justify-between items-center">
        <div className="flex items-center">
          <Input
            placeholder="请输入"
            className="w-52 mr-2"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            allowClear
          />
          <Select
            placeholder="筛选角色"
            className="w-36"
            value={roleFilter}
            onChange={setRoleFilter}
            allowClear
          >
            <Option value="管理员">管理员</Option>
            <Option value="普通用户">普通用户</Option>
          </Select>
        </div>
        <div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            className="mr-2"
            onClick={() => setAddUserOpen(true)}
          >
            新建用户
          </Button>
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={filteredUsers}
        loading={loading}
        rowKey="userId"
        pagination={{
          pageSize: pagination.pageSize,
          current: pagination.page,
          total: pagination.total,
          onChange: (page, pageSize) => {
            setPagination({
              ...pagination,
              page,
              pageSize,
            });
          },
        }}
      />
      <AddUserModal
        open={addUserOpen}
        onOk={handleAddUser}
        onCancel={() => setAddUserOpen(false)}
        confirmLoading={addUserLoading}
      />
    </div>
  );
};

export default UserManagement;
