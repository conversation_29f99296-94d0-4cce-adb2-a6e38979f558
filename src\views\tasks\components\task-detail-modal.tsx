import React from 'react';
import { Modal, Button, Progress, Tag, Descriptions, Tabs } from 'antd';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import styles from '../index.module.css';

interface TaskData {
  id: string;
  taskName: string;
  taskType: string;
  participants: string;
  status: 'running' | 'completed' | 'failed' | 'pending';
  createTime: string;
}

interface TaskDetailModalProps {
  open: boolean;
  onClose: () => void;
  task: TaskData | null;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({ open, onClose, task }) => {
  if (!task) return null;

  // 模拟任务详细信息
  const taskDetail = {
    ...task,
    algorithm: 'FedAvg',
    dataset: '数据集1',
    rounds: 100,
    currentRound: 45,
    learningRate: 0.001,
    batchSize: 32,
    participants: ['Alice', 'Bob', 'Charlie'],
    progress: 45,
    accuracy: 0.892,
    loss: 0.234,
    description: '这是一个联邦学习训练任务，用于训练图像分类模型。',
    logs: `[2025-07-01 14:00:00] 任务开始执行
[2025-07-01 14:00:05] 初始化参与方连接
[2025-07-01 14:00:10] Alice 连接成功
[2025-07-01 14:00:12] Bob 连接成功  
[2025-07-01 14:00:15] Charlie 连接成功
[2025-07-01 14:01:00] 开始第1轮训练
[2025-07-01 14:02:30] 第1轮训练完成，准确率: 0.756
[2025-07-01 14:02:35] 开始第2轮训练
[2025-07-01 14:04:10] 第2轮训练完成，准确率: 0.782
...
[2025-07-01 15:30:20] 第45轮训练完成，准确率: 0.892
[2025-07-01 15:30:25] 当前训练进度: 45/100`,
  };

  const renderStatus = (status: string) => {
    const statusConfig = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      pending: { color: 'default', text: '等待中' },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const tabItems = [
    {
      key: '1',
      label: '基本信息',
      children: (
        <div className={styles.detailSection}>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="任务ID">{taskDetail.id}</Descriptions.Item>
            <Descriptions.Item label="任务名称">{taskDetail.taskName}</Descriptions.Item>
            <Descriptions.Item label="任务类型">{taskDetail.taskType}</Descriptions.Item>
            <Descriptions.Item label="状态">{renderStatus(taskDetail.status)}</Descriptions.Item>
            <Descriptions.Item label="算法">{taskDetail.algorithm}</Descriptions.Item>
            <Descriptions.Item label="数据集">{taskDetail.dataset}</Descriptions.Item>
            <Descriptions.Item label="学习率">{taskDetail.learningRate}</Descriptions.Item>
            <Descriptions.Item label="批次大小">{taskDetail.batchSize}</Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>{taskDetail.createTime}</Descriptions.Item>
            <Descriptions.Item label="参与方" span={2}>
              {taskDetail.participants.map((p, index) => (
                <Tag key={index} color="blue">{p}</Tag>
              ))}
            </Descriptions.Item>
            <Descriptions.Item label="任务描述" span={2}>{taskDetail.description}</Descriptions.Item>
          </Descriptions>
        </div>
      ),
    },
    {
      key: '2',
      label: '训练进度',
      children: (
        <div className={styles.detailSection}>
          <div className={styles.progressSection}>
            <h4>训练进度</h4>
            <Progress 
              percent={taskDetail.progress} 
              status={taskDetail.status === 'running' ? 'active' : 'normal'}
              format={() => `${taskDetail.currentRound}/${taskDetail.rounds} 轮`}
            />
            <div style={{ marginTop: 16 }}>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>当前轮数:</span>
                <span className={styles.detailValue}>{taskDetail.currentRound}/{taskDetail.rounds}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>当前准确率:</span>
                <span className={styles.detailValue}>{(taskDetail.accuracy * 100).toFixed(2)}%</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>当前损失:</span>
                <span className={styles.detailValue}>{taskDetail.loss.toFixed(4)}</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: '3',
      label: '执行日志',
      children: (
        <div className={styles.detailSection}>
          <div className={styles.logSection}>
            <pre className={styles.logContent}>{taskDetail.logs}</pre>
          </div>
        </div>
      ),
    },
  ];

  return (
    <Modal
      title={`任务详情 - ${task.taskName}`}
      open={open}
      onCancel={onClose}
      width={900}
      className={styles.detailModal}
      footer={[
        <Button key="refresh" icon={<ReloadOutlined />}>
          刷新
        </Button>,
        <Button key="download" type="primary" icon={<DownloadOutlined />}>
          下载结果
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <Tabs defaultActiveKey="1" items={tabItems} />
    </Modal>
  );
};

export default TaskDetailModal;
