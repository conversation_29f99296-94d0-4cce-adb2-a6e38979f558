import React from 'react';
import { Modal, Form, Input, Select, Button, Space } from 'antd';

interface AddDataSourceModalProps {
  open: boolean;
  onClose: () => void;
}

const AddDataSourceModal: React.FC<AddDataSourceModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('Form values:', values);
      // 这里处理提交逻辑
      form.resetFields();
      onClose();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title="添加数据源"
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          dataSourceType: 'LOCAL',
        }}
      >
        <Form.Item
          name="dataSourceName"
          label="数据源名称"
          rules={[{ required: true, message: '请输入数据源名称' }]}
        >
          <Input placeholder="请输入数据源名称" />
        </Form.Item>

        <Form.Item
          name="dataSourceType"
          label="数据源类型"
          rules={[{ required: true, message: '请选择数据源类型' }]}
        >
          <Select
            options={[
              { value: 'LOCAL', label: 'LOCAL' },
              { value: 'REMOTE', label: 'REMOTE' },
              { value: 'MYSQL', label: 'MySQL' },
              { value: 'POSTGRESQL', label: 'PostgreSQL' },
              { value: 'ORACLE', label: 'Oracle' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="connectionString"
          label="连接字符串"
          rules={[{ required: true, message: '请输入连接字符串' }]}
        >
          <Input.TextArea 
            placeholder="请输入数据库连接字符串"
            rows={3}
          />
        </Form.Item>

        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入数据库用户名" />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password placeholder="请输入数据库密码" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
        >
          <Input.TextArea 
            placeholder="请输入数据源描述（可选）"
            rows={2}
          />
        </Form.Item>

        <Form.Item>
          <Space className="w-full justify-end">
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit}>
              确定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddDataSourceModal;
