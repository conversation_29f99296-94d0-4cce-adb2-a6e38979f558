import React, { useState, useEffect } from 'react';
import {
  Table,
  Form,
  Input,
  Button,
  Space,
  Select,
  Badge,
  DatePicker,
  Typography,
  Row,
  Col,
} from 'antd';
import { UpOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface LogRecord {
  key: string;
  taskType: string;
  taskName: string;
  status: string;
  operationTime: string;
}

interface FormValues {
  dateRange?: [Date, Date];
  taskType: string;
  taskName: string;
  taskStatus: string;
}

const LogPage: React.FC = () => {
  const [form] = Form.useForm<FormValues>();
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<LogRecord[]>([]);

  // 模拟数据加载
  useEffect(() => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      const sampleData: LogRecord[] = [
        {
          key: '1',
          taskType: '训练',
          taskName: 'test',
          status: '失败',
          operationTime: '2025-03-11 10:39:33',
        },
        {
          key: '2',
          taskType: '训练',
          taskName: '逻辑回归',
          status: '成功',
          operationTime: '2025-03-10 16:22:48',
        },
        {
          key: '3',
          taskType: '训练',
          taskName: '测试',
          status: '失败',
          operationTime: '2025-03-10 16:09:18',
        },
      ];
      setData(sampleData);
      setLoading(false);
    }, 300);
  }, []);

  const taskTypeOptions = [
    { label: '训练', value: 'train' },
    { label: '测评', value: 'evaluate' },
    { label: '优化', value: 'optimize' },
  ];

  const statusOptions = [
    { label: '成功', value: 'success' },
    { label: '失败', value: 'fail' },
  ];

  const columns: ColumnsType<LogRecord> = [
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const badgeStatus = status === '成功' ? 'success' : 'error';

        return (
          <Space>
            <Badge status={badgeStatus} text={status} />
          </Space>
        );
      },
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
    },
    {
      title: '操作',
      key: 'action',
      render: () => <Typography.Link>查看</Typography.Link>,
    },
  ];

  const handleSearch = () => {
    // Implement search logic
    form.validateFields().then((values) => {
      console.log('Search with:', values);
    });
  };

  const handleReset = () => {
    form.resetFields();
  };

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div className="p-6">
      <div className="bg-white p-4 mb-4 rounded shadow">
        <Form
          form={form}
          onFinish={handleSearch}
          initialValues={{
            taskType: 'all',
            taskStatus: 'all',
          }}
        >
          <Row>
            <Col span={8}>
              <Form.Item
                name="dateRange"
                label="日期范围"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <RangePicker />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item name="taskType" label="任务类型">
                <Select style={{ width: 200 }}>
                  <Option value="all">全部</Option>
                  {taskTypeOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item name="taskName" label="任务名称">
                <Input placeholder="请输入任务名称" style={{ width: 200 }} />
              </Form.Item>
            </Col>
          </Row>

          {collapsed && (
            <Row>
              <Col span={8}>
                <Form.Item name="taskStatus" label="任务状态">
                  <Select style={{ width: 200 }}>
                    <Option value="all">全部</Option>
                    {statusOptions.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          <div className="flex justify-end w-full">
            <Space>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button
                type="link"
                icon={<UpOutlined rotate={collapsed ? 0 : 180} />}
                onClick={toggleCollapse}
              >
                {collapsed ? '收起' : '展开'}
              </Button>
            </Space>
          </div>
        </Form>
      </div>

      <Table<LogRecord>
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{
          total: data.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </div>
  );
};

export default LogPage;
