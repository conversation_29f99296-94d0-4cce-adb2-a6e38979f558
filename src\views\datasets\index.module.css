.datasetContainer {
  padding: 24px;
  background-color: #fff;
  min-height: 100vh;
}

.searchBar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.searchInput {
  width: 240px;
}

.filterSelect {
  width: 160px;
}

.addButton {
  margin-left: auto;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.statusDotRunning {
  background-color: #52c41a;
}

.statusDotStopped {
  background-color: #d9d9d9;
}

.operationButtons {
  display: flex;
  gap: 4px;
}

.table {
  background: #fff;
  border-radius: 6px;
}

.modalForm {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.authTable {
  margin-top: 16px;
}

.addAuthButton {
  margin-bottom: 16px;
}
