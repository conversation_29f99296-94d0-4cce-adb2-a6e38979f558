import { useState } from 'react';
import {
  Avatar,
  Dropdown,
  Menu,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Button,
  message,
} from 'antd';
import { UserOutlined, LockOutlined, LogoutOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { logout } from '@/views/login/login.service';
import useUserStore from '@/store/user';
// Define interface for password form values
interface PasswordFormValues {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const PageHeader = () => {
  const [open, setOpen] = useState(false);
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);
  const [passwordForm] = Form.useForm<PasswordFormValues>();
  const navigate = useNavigate();
  const { user } = useUserStore();

  const handleMenuClick = (key: string) => {
    if (key === 'changePassword') {
      // Show password change modal
      setIsPasswordModalVisible(true);
    } else if (key === 'logout') {
      // Show logout confirmation
      Modal.confirm({
        title: '确认退出',
        content: '您确定要退出登录吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // Handle logout action
          // logout().then(() => {
          //   console.log('User confirmed logout');
          //   // Add actual logout logic here
          //   message.success('已成功退出登录');
          //   navigate('/login');
          // });
          navigate('/login');
        },
      });
    }
    setOpen(false);
  };

  const handlePasswordChange = (values: PasswordFormValues) => {
    console.log('Password change form values:', values);

    // Validate that new password and confirm password match
    if (values.newPassword !== values.confirmPassword) {
      message.error('新密码与确认密码不匹配');
      return;
    }

    // Add actual password change logic here

    message.success('密码修改成功');
    setIsPasswordModalVisible(false);
    passwordForm.resetFields();
  };

  const menu = (
    <Menu
      onClick={({ key }) => handleMenuClick(key as string)}
      items={[
        {
          key: 'changePassword',
          icon: <LockOutlined />,
          label: '修改密码',
        },
        {
          key: 'logout',
          icon: <LogoutOutlined />,
          label: '退出登录',
        },
      ]}
    />
  );

  return (
    <div className="flex justify-between items-center h-full">
      <div className="flex items-center">
        <Typography.Title level={4} style={{ margin: 0 }}>
          大模型隐私保护计算平台
        </Typography.Title>
      </div>
      <div>
        <Dropdown overlay={menu} onOpenChange={setOpen} open={open} trigger={['hover']}>
          <div className="cursor-pointer">
            <Space>
              <Avatar icon={<UserOutlined />} />
              <Typography.Text>{user?.username}</Typography.Text>
            </Space>
          </div>
        </Dropdown>
      </div>

      {/* Password Change Modal */}
      <Modal
        title="修改密码"
        open={isPasswordModalVisible}
        onCancel={() => {
          setIsPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
      >
        <Form form={passwordForm} layout="vertical" onFinish={handlePasswordChange}>
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码长度不能少于8个字符' },
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请确认新密码" />
          </Form.Item>
          <Form.Item className="mb-0 text-right">
            <Button
              type="default"
              className="mr-2"
              onClick={() => {
                setIsPasswordModalVisible(false);
                passwordForm.resetFields();
              }}
            >
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              确认修改
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PageHeader;
