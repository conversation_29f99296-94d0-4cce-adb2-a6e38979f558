import { Card, Row, Col, Table, Button, Tag, Space } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import styles from './index.module.css';

// 临时的图表占位组件，实际使用时需要安装 echarts 和 echarts-for-react
// npm install echarts echarts-for-react
import ReactECharts from 'echarts-for-react';

interface TaskRecord {
  key: string;
  taskId: string;
  taskName: string;
  executionNode: string;
  status: string;
  startTime: string;
}

const PageHome = () => {
  // 统计卡片数据
  const statsData = [
    {
      title: '总任务数',
      value: '1,234',
      trend: { type: 'up', value: '12%', text: '较上周增长' },
      color: '#1890ff',
    },
    {
      title: '活跃节点',
      value: '89',
      trend: { type: 'up', value: '5%', text: '较上周增长' },
      color: '#52c41a',
    },
    {
      title: 'CPU 使用率',
      value: '78%',
      trend: { type: 'warning', text: '接近警戒值' },
      color: '#faad14',
    },
    {
      title: '内存使用率',
      value: '65%',
      trend: { type: 'normal', text: '运行正常' },
      color: '#52c41a',
    },
  ];

  // 任务执行情况图表配置
  const taskExecutionOption = {
    title: {
      text: '任务执行情况',
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['成功', '失败', '等待中'],
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '成功',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210],
        itemStyle: { color: '#52c41a' },
      },
      {
        name: '失败',
        type: 'line',
        data: [20, 25, 18, 22, 15, 28, 25],
        itemStyle: { color: '#ff4d4f' },
      },
      {
        name: '等待中',
        type: 'line',
        data: [50, 40, 35, 45, 80, 130, 110],
        itemStyle: { color: '#faad14' },
      },
    ],
  };

  // 节点状态分布饼图配置
  const nodeStatusOption = {
    title: {
      text: '节点状态分布',
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'middle',
      data: ['运行中', '离线', '维护中', '异常'],
    },
    series: [
      {
        name: '节点状态',
        type: 'pie',
        radius: '50%', // 改为普通饼图，不是圆环
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true, // 显示标签
          position: 'outside', // 标签位置在外部
          formatter: '{b}: {c}', // 标签格式：名称: 数值 (百分比)
          fontSize: 12,
          fontWeight: 'normal',
        },
        labelLine: {
          show: true, // 显示延线
          length: 15, // 第一段延线长度
          length2: 10, // 第二段延线长度
          smooth: false, // 延线不使用平滑曲线
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        data: [
          { value: 45, name: '运行中', itemStyle: { color: '#52c41a' } },
          { value: 20, name: '离线', itemStyle: { color: '#ff4d4f' } },
          { value: 15, name: '维护中', itemStyle: { color: '#faad14' } },
          { value: 9, name: '异常', itemStyle: { color: '#f5222d' } },
        ],
      },
    ],
  };

  // 最近任务表格数据
  const recentTasksData = [
    {
      key: '1',
      taskId: 'TASK-2023121501',
      taskName: '数据同步任务',
      executionNode: 'Node-01',
      status: 'running',
      startTime: '2023-12-15 10:30:00',
    },
    {
      key: '2',
      taskId: 'TASK-2023121502',
      taskName: '系统备份',
      executionNode: 'Node-02',
      status: 'waiting',
      startTime: '2023-12-15 11:00:00',
    },
    {
      key: '3',
      taskId: 'TASK-2023121503',
      taskName: '日志分析',
      executionNode: 'Node-03',
      status: 'failed',
      startTime: '2023-12-15 09:15:00',
    },
  ];

  // 表格列配置
  const taskColumns = [
    {
      title: '任务 ID',
      dataIndex: 'taskId',
      key: 'taskId',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '执行节点',
      dataIndex: 'executionNode',
      key: 'executionNode',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          running: { color: 'green', text: '运行中' },
          waiting: { color: 'orange', text: '等待中' },
          failed: { color: 'red', text: '失败' },
          completed: { color: 'blue', text: '已完成' },
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TaskRecord) => (
        <Space size="small">
          <Button type="link" size="small">
            查看
          </Button>
          {record.status === 'running' && (
            <Button type="link" size="small">
              停止
            </Button>
          )}
          {record.status === 'waiting' && (
            <Button type="link" size="small">
              取消
            </Button>
          )}
          {record.status === 'failed' && (
            <Button type="link" size="small">
              重试
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.homeContainer}>
      {/* 统计卡片区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {statsData.map((stat, index) => (
          <Col xs={24} sm={12} md={6} key={index}>
            <Card className={styles.statsCard}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                }}
              >
                <div>
                  <div className={styles.statsTitle}>{stat.title}</div>
                  <div className={styles.statsValue}>{stat.value}</div>
                  <div className={styles.statsTrend}>
                    {stat.trend.type === 'up' && (
                      <>
                        <ArrowUpOutlined
                          style={{ color: '#52c41a', marginRight: '4px' }}
                        />
                        <span style={{ color: '#52c41a' }}>
                          {stat.trend.text} {stat.trend.value}
                        </span>
                      </>
                    )}
                    {stat.trend.type === 'down' && (
                      <>
                        <ArrowDownOutlined
                          style={{ color: '#ff4d4f', marginRight: '4px' }}
                        />
                        <span style={{ color: '#ff4d4f' }}>
                          {stat.trend.text} {stat.trend.value}
                        </span>
                      </>
                    )}
                    {stat.trend.type === 'warning' && (
                      <>
                        <WarningOutlined
                          style={{ color: '#faad14', marginRight: '4px' }}
                        />
                        <span style={{ color: '#faad14' }}>{stat.trend.text}</span>
                      </>
                    )}
                    {stat.trend.type === 'normal' && (
                      <>
                        <CheckCircleOutlined
                          style={{ color: '#52c41a', marginRight: '4px' }}
                        />
                        <span style={{ color: '#52c41a' }}>{stat.trend.text}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card className={styles.chartCard}>
            <ReactECharts option={taskExecutionOption} style={{ height: '400px' }} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card className={styles.chartCard}>
            <ReactECharts option={nodeStatusOption} style={{ height: '400px' }} />
          </Card>
        </Col>
      </Row>

      {/* 最近任务表格 */}
      <Card
        title="最近任务"
        extra={<Button type="primary">查看全部</Button>}
        className={styles.tableCard}
      >
        <Table
          columns={taskColumns}
          dataSource={recentTasksData}
          pagination={false}
          size="middle"
        />
      </Card>
    </div>
  );
};

export default PageHome;
