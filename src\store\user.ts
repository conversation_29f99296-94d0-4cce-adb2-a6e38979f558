import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface UserInfo {
  username: string;
  role: number;
  token: string;
}

interface UserStore {
  user: UserInfo | null;
  setUser: (user: UserInfo) => void;
}

const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user: UserInfo) => set({ user }),
    }),
    {
      name: 'userInfo',
      // storage: createJSONStorage(() => localStorage),
    },
  ),
);

export default useUserStore;
